# conftest.py 简化优化任务

## 任务背景
用户询问 conftest.py 是否还有必要保留，经分析发现其中包含 Dashboard 核心功能的实现。

## 问题分析
原 conftest.py 包含多个功能：
- ✅ **browser_type_launch_args**: Dashboard 无头模式的核心实现
- ✅ **browser_context_args**: 浏览器视口和HTTPS配置
- ❌ **pytest_addoption**: Dashboard 不使用的命令行参数
- ❌ **page_setup**: 测试文件未使用的页面设置
- ❌ **api_context**: 无API测试使用

## 解决方案
保留必要功能，删除冗余代码

## 执行步骤
1. ✅ 移除 pytest_addoption 自定义命令行参数
2. ✅ 移除 page_setup fixture（未被使用）
3. ✅ 移除 api_context fixture（无API测试）
4. ✅ 简化 browser_type_launch_args 实现
5. ✅ 保留 browser_context_args 配置

## 简化后的 conftest.py

```python
"""
pytest 配置文件 - 简化版，只保留必要功能
"""
import os
import pytest

@pytest.fixture(scope="session")
def browser_context_args(browser_context_args):
    """浏览器上下文配置"""
    return {
        **browser_context_args,
        "viewport": {"width": 1920, "height": 1080},
        "ignore_https_errors": True,
    }

@pytest.fixture(scope="session")
def browser_type_launch_args(browser_type_launch_args):
    """
    浏览器启动参数配置 - 支持 Dashboard 的动态 headless 控制
    
    通过环境变量 PLAYWRIGHT_HEADLESS 控制无头模式
    """
    # 检查环境变量（Dashboard 设置）
    env_headless = os.getenv("PLAYWRIGHT_HEADLESS", "").lower().strip()
    headless = env_headless in ("true", "1", "yes", "on")

    return {
        **browser_type_launch_args,
        "channel": "chrome",  # 使用系统安装的 Chrome
        "headless": headless, # 动态控制无头模式
        "slow_mo": 100,       # 操作间隔100ms，便于观察
    }
```

## 保留的核心功能
- ✅ Dashboard 无头模式控制（环境变量 PLAYWRIGHT_HEADLESS）
- ✅ Chrome 浏览器配置
- ✅ 合理的视口大小设置
- ✅ HTTPS 错误忽略

## 预期效果
- ✅ 保持 Dashboard 所有功能正常
- ✅ 简化代码，移除冗余配置
- ✅ 提高代码可维护性

## 完成时间
2025-07-31

## 状态
✅ 已完成
