"""
pytest 配置文件 - 简化版，只保留必要功能
"""
import os
import pytest


@pytest.fixture(scope="session")
def browser_context_args(browser_context_args):
    """浏览器上下文配置"""
    return {
        **browser_context_args,
        "viewport": {"width": 1920, "height": 1080},
        "ignore_https_errors": True,
    }


@pytest.fixture(scope="session")
def browser_type_launch_args(browser_type_launch_args):
    """
    浏览器启动参数配置 - 支持 Dashboard 的动态 headless 控制

    通过环境变量 PLAYWRIGHT_HEADLESS 控制无头模式
    """
    # 检查环境变量（Dashboard 设置）
    env_headless = os.getenv("PLAYWRIGHT_HEADLESS", "").lower().strip()
    headless = env_headless in ("true", "1", "yes", "on")

    return {
        **browser_type_launch_args,
        "channel": "chrome",  # 使用系统安装的 Chrome
        "headless": headless, # 动态控制无头模式
        "slow_mo": 100,       # 操作间隔100ms，便于观察
    }