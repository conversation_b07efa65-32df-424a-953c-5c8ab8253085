from playwright.sync_api import Page, expect


def test_example1(page: Page) -> None:
    page.goto("http://10.12.135.167:9089/#/login")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("ROOT")
    page.get_by_placeholder("请输入密码").click()
    page.get_by_placeholder("请输入密码").fill("sA123456@")
    page.get_by_role("button", name="登录").click()
    expect(page.locator(".frame-vlayout-nav-logo")).to_be_visible()


def test_example2(page: Page) -> None:
    page.goto("http://10.12.135.167:9089/#/projectmanage")
    page.get_by_role("button", name=" 新建项目").click()
    page.get_by_placeholder("请输入项目名称").click()
    page.get_by_placeholder("请输入项目名称").fill("1234")
    page.get_by_label("场景类型").click()
    page.get_by_role("listitem").nth(1).click()
    page.get_by_role("button", name="确定").click()
    expect(page.get_by_text("1234")).to_be_visible()


def test_create_simulation_task(page: Page) -> None:
    """
    测试创建新仿真任务的完整流程

    流程：
    1. 登录系统
    2. 进入项目管理页面
    3. 选择一个项目并点击详情
    4. 进入仿真任务页面
    5. 点击新增按钮
    6. 填写任务名称
    7. 保存任务
    8. 验证任务创建成功
    """

    # 步骤1: 登录系统
    page.goto("http://10.12.135.167:9089/#/login")
    page.get_by_placeholder("请输入账号").click()
    page.get_by_placeholder("请输入账号").fill("ROOT")
    page.get_by_placeholder("请输入密码").click()
    page.get_by_placeholder("请输入密码").fill("sA123456@")
    page.get_by_role("button", name="登录").click()

    # 验证登录成功 - 等待导航栏logo出现
    expect(page.locator(".frame-vlayout-nav-logo")).to_be_visible()

    # 步骤2: 确保在项目管理页面
    # 系统登录后默认会跳转到项目管理页面 (/projectmanage)
    # 等待项目列表加载完成
    page.wait_for_load_state("networkidle")

    # 步骤3: 选择第一个项目并点击详情
    # 等待项目卡片加载
    project_cards = page.locator(".el-col .el-card")
    expect(project_cards.first()).to_be_visible()

    # 点击第一个项目的详情按钮
    first_project_detail_btn = project_cards.first().get_by_role("button", name="详情")
    expect(first_project_detail_btn).to_be_visible()
    first_project_detail_btn.click()

    # 步骤4: 验证进入仿真任务页面
    # 等待仿真任务页面加载，查找新增按钮
    new_task_button = page.get_by_role("button", name="新增")
    expect(new_task_button).to_be_visible()

    # 步骤5: 点击新增按钮
    new_task_button.click()

    # 步骤6: 填写任务名称
    # 等待弹窗出现
    task_name_input = page.get_by_placeholder("请输入任务名称")
    expect(task_name_input).to_be_visible(timeout=5000)

    # 生成唯一的任务名称（使用时间戳）
    task_name = f"自动化测试任务_{int(time.time())}"
    task_name_input.fill(task_name)

    # 步骤7: 保存任务
    # 点击确定按钮
    confirm_button = page.get_by_role("button", name="确定")
    expect(confirm_button).to_be_visible()
    confirm_button.click()

    # 步骤8: 验证任务创建成功
    # 等待成功提示消息
    success_message = page.locator(".el-message--success")
    expect(success_message).to_be_visible(timeout=5000)
    expect(success_message).to_contain_text("保存成功")

    # 验证弹窗关闭
    expect(task_name_input).not_to_be_visible(timeout=5000)

    # 验证任务列表中出现新创建的任务
    # 等待任务列表刷新
    page.wait_for_load_state("networkidle")

    # 在任务列表中查找新创建的任务
    task_table = page.locator(".el-table")
    expect(task_table).to_be_visible()

    # 验证任务名称出现在表格中
    task_row = page.locator(f"text={task_name}")
    expect(task_row).to_be_visible(timeout=10000)
